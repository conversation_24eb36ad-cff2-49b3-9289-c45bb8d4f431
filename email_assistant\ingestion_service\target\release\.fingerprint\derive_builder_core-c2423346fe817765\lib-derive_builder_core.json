{"rustc": 1842507548689473721, "features": "[\"lib_has_std\"]", "declared_features": "[\"alloc\", \"clippy\", \"lib_has_std\"]", "target": 15805722739128704647, "profile": 1369601567987815722, "path": 11944414702791965715, "deps": [[496455418292392305, "darling", false, 13956392509168856682], [3060637413840920116, "proc_macro2", false, 11922922787390032898], [4974441333307933176, "syn", false, 13789083950146257148], [17990358020177143287, "quote", false, 2646576083173682583]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\derive_builder_core-c2423346fe817765\\dep-lib-derive_builder_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}