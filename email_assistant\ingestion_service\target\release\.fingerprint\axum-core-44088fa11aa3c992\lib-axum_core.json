{"rustc": 1842507548689473721, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 2040997289075261528, "path": 13067367211683519187, "deps": [[784494742817713399, "tower_service", false, 5062019019135106002], [1906322745568073236, "pin_project_lite", false, 2389057907062384012], [2517136641825875337, "sync_wrapper", false, 8905282965693471066], [7712452662827335977, "tower_layer", false, 4087051148562280967], [7858942147296547339, "rustversion", false, 17578913925582313715], [8606274917505247608, "tracing", false, 18139136616817915350], [9010263965687315507, "http", false, 1862789691165185595], [10229185211513642314, "mime", false, 9651018063964816897], [10629569228670356391, "futures_util", false, 6321976705103560301], [11946729385090170470, "async_trait", false, 4534693507436567389], [14084095096285906100, "http_body", false, 15356474674297432158], [16066129441945555748, "bytes", false, 5746740623261701503], [16900715236047033623, "http_body_util", false, 7220491532981291408]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\axum-core-44088fa11aa3c992\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}