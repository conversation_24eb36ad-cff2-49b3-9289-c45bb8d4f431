{"rustc": 1842507548689473721, "features": "[\"lib_has_std\"]", "declared_features": "[\"alloc\", \"clippy\", \"lib_has_std\"]", "target": 15229808779680689443, "profile": 1369601567987815722, "path": 5937674952582547309, "deps": [[4003231138667150418, "derive_builder_core", false, 4299167758088583410], [4974441333307933176, "syn", false, 13789083950146257148]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\derive_builder_macro-81ee46758a90e465\\dep-lib-derive_builder_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}