# AI-Assisted Email Response System - Complete Service Startup Script
# This script starts all required services and the desktop application in external console windows

param(
    [switch]$Verbose,
    [switch]$SkipDesktop,
    [int]$Timeout = 60
)

Write-Host "AI-Assisted Email Response System - Complete Startup" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Green
Write-Host ""

# Configuration
$ProjectRoot = $PSScriptRoot
$LogFile = Join-Path $ProjectRoot "logs\startup.log"

# Ensure logs directory exists
$LogsDir = Split-Path $LogFile
if (-not (Test-Path $LogsDir)) {
    New-Item -ItemType Directory -Path $LogsDir -Force | Out-Null
}

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    Add-Content -Path $LogFile -Value $logEntry
    
    switch ($Level) {
        "ERROR" { Write-Host $logEntry -ForegroundColor Red }
        "WARN" { Write-Host $logEntry -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logEntry -ForegroundColor Green }
        "INFO" { Write-Host $logEntry -ForegroundColor Cyan }
        default { Write-Host $logEntry }
    }
}

function Test-Port {
    param([int]$Port, [int]$TimeoutSeconds = 5)
    
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $asyncResult = $tcpClient.BeginConnect("localhost", $Port, $null, $null)
        $wait = $asyncResult.AsyncWaitHandle.WaitOne($TimeoutSeconds * 1000, $false)
        
        if ($wait) {
            $tcpClient.EndConnect($asyncResult)
            $tcpClient.Close()
            return $true
        } else {
            $tcpClient.Close()
            return $false
        }
    } catch {
        return $false
    }
}

function Test-ServiceHealth {
    param([string]$HealthUrl, [int]$TimeoutSeconds = 10)
    
    if (-not $HealthUrl) {
        return $false
    }
    
    try {
        $response = Invoke-WebRequest -Uri $HealthUrl -TimeoutSec $TimeoutSeconds -ErrorAction Stop
        return $response.StatusCode -eq 200
    } catch {
        return $false
    }
}

function Start-ServiceInConsole {
    param(
        [string]$ServiceName,
        [string]$WorkingDir,
        [string]$Command,
        [string]$WindowTitle,
        [int]$Port,
        [string]$HealthUrl,
        [int]$StartupTime
    )
    
    Write-Log "Starting $ServiceName in external console..."
    
    # Check if already running
    if ($Port -gt 0 -and (Test-Port -Port $Port -TimeoutSeconds 3)) {
        Write-Log "$ServiceName is already running on port $Port" "SUCCESS"
        return $true
    }
    
    # Validate working directory
    if (-not (Test-Path $WorkingDir)) {
        Write-Log "Working directory not found: $WorkingDir" "ERROR"
        return $false
    }
    
    try {
        $startupCmd = "Set-Location '$WorkingDir'; `$Host.UI.RawUI.WindowTitle = '$WindowTitle'; Write-Host 'Starting $ServiceName...' -ForegroundColor Green; $Command"
        
        $startInfo = New-Object System.Diagnostics.ProcessStartInfo
        $startInfo.FileName = "powershell.exe"
        $startInfo.Arguments = "-NoExit -Command `"$startupCmd`""
        $startInfo.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Normal
        $startInfo.CreateNoWindow = $false
        
        $process = [System.Diagnostics.Process]::Start($startInfo)
        Write-Log "$ServiceName started in external console with PID $($process.Id)" "SUCCESS"
        
        # Wait for service to become healthy
        if ($StartupTime -gt 0) {
            Write-Log "Waiting for $ServiceName to become healthy (max $StartupTime seconds)..."
            
            $elapsed = 0
            $interval = 2
            
            while ($elapsed -lt $StartupTime) {
                if ($Port -gt 0 -and (Test-Port -Port $Port)) {
                    if ($HealthUrl -and (Test-ServiceHealth -HealthUrl $HealthUrl)) {
                        Write-Log "$ServiceName is healthy and ready" "SUCCESS"
                        return $true
                    } elseif (-not $HealthUrl) {
                        Write-Log "$ServiceName is ready on port $Port" "SUCCESS"
                        return $true
                    }
                }
                
                Start-Sleep -Seconds $interval
                $elapsed += $interval
                
                if ($Verbose) {
                    Write-Log "Waiting for $ServiceName... ($elapsed/$StartupTime seconds)"
                }
            }
            
            Write-Log "$ServiceName may not be fully ready, but continuing..." "WARN"
        }
        
        return $true
        
    } catch {
        Write-Log "Failed to start $ServiceName : $_" "ERROR"
        return $false
    }
}

function Test-Prerequisites {
    Write-Log "Checking system prerequisites..."
    
    $allGood = $true
    
    # Check Python
    try {
        $null = Get-Command python -ErrorAction Stop
        Write-Log "Python is available" "SUCCESS"
    } catch {
        Write-Log "Python is required but not found" "ERROR"
        $allGood = $false
    }
    
    # Check Node.js
    try {
        $null = Get-Command node -ErrorAction Stop
        Write-Log "Node.js is available" "SUCCESS"
    } catch {
        Write-Log "Node.js is required but not found" "ERROR"
        $allGood = $false
    }
    
    # Check NPM
    try {
        $null = Get-Command npm -ErrorAction Stop
        Write-Log "NPM is available" "SUCCESS"
    } catch {
        Write-Log "NPM is required but not found" "ERROR"
        $allGood = $false
    }
    
    # Check Rust/Cargo
    try {
        $null = Get-Command cargo -ErrorAction Stop
        Write-Log "Rust/Cargo is available" "SUCCESS"
    } catch {
        Write-Log "Rust/Cargo is required but not found" "ERROR"
        $allGood = $false
    }
    
    # Check for Ollama
    $ollamaPath = "C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe"
    if (Test-Path $ollamaPath) {
        $env:PATH += ";$(Split-Path $ollamaPath)"
        Write-Log "Ollama found and added to PATH" "SUCCESS"
    } else {
        Write-Log "Ollama not found at expected location" "ERROR"
        $allGood = $false
    }
    
    return $allGood
}

function Get-ServiceStatus {
    Write-Host ""
    Write-Host "Service Status Check:" -ForegroundColor Cyan
    Write-Host "================================================" -ForegroundColor Cyan
    
    $services = @(
        @{Name="Qdrant Vector Database"; Port=6333; HealthUrl="http://localhost:6333/"},
        @{Name="Ollama LLM Service"; Port=11434; HealthUrl="http://localhost:11434/api/tags"},
        @{Name="RAG Service"; Port=8003; HealthUrl="http://localhost:8003/health"},
        @{Name="Ingestion Service"; Port=8080; HealthUrl="http://localhost:8080/health"}
    )
    
    foreach ($service in $services) {
        $isHealthy = Test-ServiceHealth -HealthUrl $service.HealthUrl -TimeoutSeconds 3
        $portOpen = Test-Port -Port $service.Port -TimeoutSeconds 2
        
        $status = if ($isHealthy) { "HEALTHY" } elseif ($portOpen) { "RUNNING" } else { "STOPPED" }
        $color = if ($isHealthy) { "Green" } elseif ($portOpen) { "Yellow" } else { "Red" }
        
        Write-Host "$($service.Name) (Port $($service.Port)): $status" -ForegroundColor $color
    }
}

function Show-SystemInfo {
    Write-Host ""
    Write-Host "System Information:" -ForegroundColor Cyan
    Write-Host "================================================" -ForegroundColor Cyan
    
    # Memory usage
    try {
        $memory = Get-CimInstance -ClassName Win32_OperatingSystem
        $memoryUsage = [math]::Round(($memory.TotalVisibleMemorySize - $memory.FreePhysicalMemory) / $memory.TotalVisibleMemorySize * 100, 2)
        $memoryColor = if ($memoryUsage -gt 80) { "Red" } elseif ($memoryUsage -gt 60) { "Yellow" } else { "Green" }
        Write-Host "Memory Usage: $memoryUsage%" -ForegroundColor $memoryColor
    } catch {
        Write-Host "Memory Usage: Unable to determine" -ForegroundColor Yellow
    }
    
    # Disk usage
    try {
        $disk = Get-CimInstance -ClassName Win32_LogicalDisk | Where-Object { $_.DeviceID -eq "C:" }
        $diskUsage = [math]::Round(($disk.Size - $disk.FreeSpace) / $disk.Size * 100, 2)
        $diskColor = if ($diskUsage -gt 90) { "Red" } elseif ($diskUsage -gt 75) { "Yellow" } else { "Green" }
        Write-Host "Disk Usage (C:): $diskUsage%" -ForegroundColor $diskColor
    } catch {
        Write-Host "Disk Usage: Unable to determine" -ForegroundColor Yellow
    }
}

# Main execution
Write-Log "=== AI Email Assistant Complete Startup Script Started ===" "INFO"

try {
    # Check prerequisites
    if (-not (Test-Prerequisites)) {
        Write-Log "Prerequisites check failed. Please install missing components." "ERROR"
        exit 1
    }

    Write-Host ""
    Write-Host "Prerequisites check passed" -ForegroundColor Green

    # Service configurations
    $services = @(
        @{
            Name = "Qdrant Vector Database"
            WorkingDir = Join-Path $ProjectRoot "ingestion_service\qdrant"
            Command = ".\qdrant.exe"
            WindowTitle = "Qdrant Vector Database"
            Port = 6333
            HealthUrl = "http://localhost:6333/"
            StartupTime = 15
        },
        @{
            Name = "RAG Service"
            WorkingDir = Join-Path $ProjectRoot "rag_service"
            Command = "python main.py"
            WindowTitle = "RAG Service"
            Port = 8003
            HealthUrl = "http://localhost:8003/health"
            StartupTime = 25
        },
        @{
            Name = "Ingestion Service"
            WorkingDir = Join-Path $ProjectRoot "ingestion_service"
            Command = "cargo run --bin ingestion_server"
            WindowTitle = "Ingestion Service"
            Port = 8080
            HealthUrl = "http://localhost:8080/health"
            StartupTime = 20
        }
    )

    # Start services in order
    foreach ($service in $services) {
        Write-Host ""
        Write-Host "Starting $($service.Name)..." -ForegroundColor Blue

        $result = Start-ServiceInConsole -ServiceName $service.Name -WorkingDir $service.WorkingDir -Command $service.Command -WindowTitle $service.WindowTitle -Port $service.Port -HealthUrl $service.HealthUrl -StartupTime $service.StartupTime

        if (-not $result) {
            Write-Log "Failed to start $($service.Name)" "ERROR"
            exit 1
        }

        Start-Sleep -Seconds 3
    }

    # Start desktop application
    if (-not $SkipDesktop) {
        Write-Host ""
        Write-Host "Starting Desktop Application..." -ForegroundColor Blue

        $desktopResult = Start-ServiceInConsole -ServiceName "Desktop Application" -WorkingDir (Join-Path $ProjectRoot "desktop_app") -Command "npm run tauri:dev" -WindowTitle "Email Assistant Desktop App" -Port 0 -HealthUrl "" -StartupTime 0

        if (-not $desktopResult) {
            Write-Log "Failed to start Desktop Application" "WARN"
        }
    }

    # Show final status
    Get-ServiceStatus
    Show-SystemInfo

    Write-Host ""
    Write-Host "All services started successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Service URLs:" -ForegroundColor Cyan
    Write-Host "- Qdrant Vector DB: http://localhost:6333" -ForegroundColor White
    Write-Host "- Ollama LLM: http://localhost:11434" -ForegroundColor White
    Write-Host "- RAG Service: http://localhost:8003" -ForegroundColor White
    Write-Host "- Ingestion Service: http://localhost:8080" -ForegroundColor White
    Write-Host ""
    Write-Host "Log file: $LogFile" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Ready to process your 11GB mbox file!" -ForegroundColor Green
    Write-Host "Use the desktop app to load: f:\EMAILS\full_combined_threaded.mbox" -ForegroundColor Yellow

} catch {
    Write-Log "Startup script failed: $_" "ERROR"
    exit 1
}

Write-Log "=== Startup script completed successfully ===" "SUCCESS"
