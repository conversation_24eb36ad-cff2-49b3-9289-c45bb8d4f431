# AI-Assisted Email Response System - Production Deployment Preparation
# This script cleans up development files and prepares a production-ready package

param(
    [string]$OutputPath = ".\email_assistant_production",
    [switch]$KeepLogs,
    [switch]$Verbose
)

Write-Host "Preparing Production Deployment Package" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

$SourcePath = $PSScriptRoot
$TotalSizeSaved = 0

function Get-FolderSize {
    param([string]$Path)
    if (Test-Path $Path) {
        return (Get-ChildItem $Path -Recurse -File | Measure-Object -Property Length -Sum).Sum
    }
    return 0
}

function Remove-DevFolder {
    param([string]$Path, [string]$Description)
    
    if (Test-Path $Path) {
        $size = Get-FolderSize $Path
        $sizeMB = [math]::Round($size / 1MB, 2)
        
        Write-Host "Removing $Description ($sizeMB MB)..." -ForegroundColor Yellow
        Remove-Item $Path -Recurse -Force
        
        $script:TotalSizeSaved += $size
        
        if ($Verbose) {
            Write-Host "  Removed: $Path" -ForegroundColor Gray
        }
    }
}

function Copy-ProductionFiles {
    Write-Host "Copying source files to production package..." -ForegroundColor Cyan
    
    # Create output directory
    if (Test-Path $OutputPath) {
        Write-Host "Removing existing output directory..." -ForegroundColor Yellow
        Remove-Item $OutputPath -Recurse -Force
    }
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    
    # Copy essential files and directories
    $essentialItems = @(
        "start_all_services.ps1",
        "start_all_services.bat",
        "README_STARTUP.md",
        "ingestion_service\src",
        "ingestion_service\Cargo.toml",
        "ingestion_service\Cargo.lock",
        "ingestion_service\qdrant",
        "rag_service\*.py",
        "rag_service\requirements.txt",
        "desktop_app\src",
        "desktop_app\src-tauri\src",
        "desktop_app\src-tauri\Cargo.toml",
        "desktop_app\src-tauri\Cargo.lock",
        "desktop_app\src-tauri\tauri.conf.json",
        "desktop_app\src-tauri\capabilities",
        "desktop_app\src-tauri\icons",
        "desktop_app\src-tauri\build.rs",
        "desktop_app\package.json",
        "desktop_app\package-lock.json",
        "desktop_app\tsconfig.json",
        "desktop_app\index.html",
        "desktop_app\public",
        "scripts\*.ps1",
        "config"
    )
    
    foreach ($item in $essentialItems) {
        $sourcePath = Join-Path $SourcePath $item
        $destPath = Join-Path $OutputPath $item
        
        if (Test-Path $sourcePath) {
            $destDir = Split-Path $destPath
            if (-not (Test-Path $destDir)) {
                New-Item -ItemType Directory -Path $destDir -Force | Out-Null
            }
            
            if (Test-Path $sourcePath -PathType Container) {
                Copy-Item $sourcePath $destPath -Recurse -Force
            } else {
                Copy-Item $sourcePath $destPath -Force
            }
            
            if ($Verbose) {
                Write-Host "  Copied: $item" -ForegroundColor Gray
            }
        }
    }
}

# Start cleanup process
Write-Host ""
Write-Host "Step 1: Analyzing current directory sizes..." -ForegroundColor Cyan

$originalSize = Get-FolderSize $SourcePath
$originalSizeMB = [math]::Round($originalSize / 1MB, 2)
Write-Host "Original directory size: $originalSizeMB MB" -ForegroundColor White

# Clean development artifacts
Write-Host ""
Write-Host "Step 2: Removing development artifacts..." -ForegroundColor Cyan

# Remove Rust build artifacts
Remove-DevFolder (Join-Path $SourcePath "ingestion_service\target") "Rust build cache"
Remove-DevFolder (Join-Path $SourcePath "desktop_app\src-tauri\target") "Tauri build cache"

# Remove Node.js artifacts
Remove-DevFolder (Join-Path $SourcePath "desktop_app\node_modules") "Node.js dependencies"
Remove-DevFolder (Join-Path $SourcePath "desktop_app\dist") "Frontend build output"

# Remove Python cache
Remove-DevFolder (Join-Path $SourcePath "rag_service\__pycache__") "Python cache"

# Remove logs (unless keeping them)
if (-not $KeepLogs) {
    Remove-DevFolder (Join-Path $SourcePath "logs") "Runtime logs"
}

# Remove test files
Remove-DevFolder (Join-Path $SourcePath "test_emails") "Test email samples"
Remove-DevFolder (Join-Path $SourcePath "tests") "Test code"

# Remove any .log files
Get-ChildItem $SourcePath -Filter "*.log" -Recurse | ForEach-Object {
    $size = $_.Length
    $sizeMB = [math]::Round($size / 1MB, 2)
    Write-Host "Removing log file: $($_.Name) ($sizeMB MB)..." -ForegroundColor Yellow
    Remove-Item $_.FullName -Force
    $script:TotalSizeSaved += $size
}

# Calculate space saved
$totalSavedMB = [math]::Round($TotalSizeSaved / 1MB, 2)
$newSize = Get-FolderSize $SourcePath
$newSizeMB = [math]::Round($newSize / 1MB, 2)

Write-Host ""
Write-Host "Cleanup Summary:" -ForegroundColor Green
Write-Host "- Original size: $originalSizeMB MB" -ForegroundColor White
Write-Host "- Cleaned size: $newSizeMB MB" -ForegroundColor White
Write-Host "- Space saved: $totalSavedMB MB" -ForegroundColor Green

# Create production package
Write-Host ""
Write-Host "Step 3: Creating production package..." -ForegroundColor Cyan
Copy-ProductionFiles

$productionSize = Get-FolderSize $OutputPath
$productionSizeMB = [math]::Round($productionSize / 1MB, 2)

Write-Host ""
Write-Host "Production Package Created!" -ForegroundColor Green
Write-Host "Location: $OutputPath" -ForegroundColor White
Write-Host "Size: $productionSizeMB MB" -ForegroundColor Green

# Create deployment instructions
$deploymentInstructions = @"
# Production Deployment Instructions

## Package Contents
- Source code for all services
- Configuration files
- Startup scripts
- Vector database binaries

## Prerequisites on Target Machine
1. Python 3.8+ with pip
2. Node.js 18+ with npm
3. Rust toolchain (rustc, cargo)
4. Ollama LLM service

## Deployment Steps
1. Copy this entire folder to target machine
2. Install Python dependencies: ``cd rag_service && pip install -r requirements.txt``
3. Install Node.js dependencies: ``cd desktop_app && npm install``
4. Build Rust services: ``cd ingestion_service && cargo build --release``
5. Build desktop app: ``cd desktop_app && npm run tauri:build``
6. Run startup script: ``.\start_all_services.ps1``

## Estimated Build Time on Target Machine
- Python dependencies: 2-5 minutes
- Node.js dependencies: 3-10 minutes  
- Rust compilation: 10-20 minutes
- Tauri build: 5-15 minutes
- Total: 20-50 minutes

## Production Notes
- All development artifacts removed
- Logs directory will be created on first run
- Vector database will initialize automatically
- Services will start in external console windows
"@

$deploymentInstructions | Out-File (Join-Path $OutputPath "DEPLOYMENT_INSTRUCTIONS.md") -Encoding UTF8

Write-Host ""
Write-Host "Deployment instructions created: DEPLOYMENT_INSTRUCTIONS.md" -ForegroundColor Cyan
Write-Host ""
Write-Host "Ready for production transfer!" -ForegroundColor Green
