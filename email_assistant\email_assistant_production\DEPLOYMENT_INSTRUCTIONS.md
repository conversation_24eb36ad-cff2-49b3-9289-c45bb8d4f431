﻿# Production Deployment Instructions

## Package Contents
- Source code for all services
- Configuration files
- Startup scripts
- Vector database binaries

## Prerequisites on Target Machine
1. Python 3.8+ with pip
2. Node.js 18+ with npm
3. Rust toolchain (rustc, cargo)
4. Ollama LLM service

## Deployment Steps
1. Copy this entire folder to target machine
2. Install Python dependencies: `cd rag_service && pip install -r requirements.txt`
3. Install Node.js dependencies: `cd desktop_app && npm install`
4. Build Rust services: `cd ingestion_service && cargo build --release`
5. Build desktop app: `cd desktop_app && npm run tauri:build`
6. Run startup script: `.\start_all_services.ps1`

## Estimated Build Time on Target Machine
- Python dependencies: 2-5 minutes
- Node.js dependencies: 3-10 minutes  
- Rust compilation: 10-20 minutes
- Tauri build: 5-15 minutes
- Total: 20-50 minutes

## Production Notes
- All development artifacts removed
- Logs directory will be created on first run
- Vector database will initialize automatically
- Services will start in external console windows
