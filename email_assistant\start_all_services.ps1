# AI-Assisted Email Response System - Complete Service Startup Script
# This script starts all required services and the desktop application in external console windows
# Created by The Augster for comprehensive system deployment

param(
    [switch]$Verbose,
    [switch]$SkipDesktop,
    [int]$Timeout = 60
)

Write-Host "🚀 AI-Assisted Email Response System - Complete Startup" -ForegroundColor Green
Write-Host "=" * 70 -ForegroundColor Green
Write-Host ""

# Configuration
$script:ProjectRoot = $PSScriptRoot
$script:LogFile = Join-Path $ProjectRoot "logs\startup.log"

# Service definitions with complete configuration
$script:Services = @{
    "qdrant" = @{
        Name = "Qdrant Vector Database"
        Port = 6333
        HealthUrl = "http://localhost:6333/"
        WorkingDir = Join-Path $ProjectRoot "ingestion_service\qdrant"
        Command = ".\qdrant.exe"
        Dependencies = @()
        StartupTime = 15
        Critical = $true
        WindowTitle = "Qdrant Vector Database"
    }
    "ollama" = @{
        Name = "Ollama LLM Service"
        Port = 11434
        HealthUrl = "http://localhost:11434/api/tags"
        WorkingDir = $env:USERPROFILE
        Command = "ollama serve"
        Dependencies = @()
        StartupTime = 20
        Critical = $true
        WindowTitle = "Ollama LLM Service"
    }
    "rag_service" = @{
        Name = "RAG Service (Python)"
        Port = 8003
        HealthUrl = "http://localhost:8003/health"
        WorkingDir = Join-Path $ProjectRoot "rag_service"
        Command = "python main.py"
        Dependencies = @("ollama")
        StartupTime = 25
        Critical = $true
        WindowTitle = "RAG Service"
    }
    "ingestion_service" = @{
        Name = "Ingestion Service (Rust)"
        Port = 8080
        HealthUrl = "http://localhost:8080/health"
        WorkingDir = Join-Path $ProjectRoot "ingestion_service"
        Command = "cargo run --bin ingestion_server"
        Dependencies = @("qdrant", "rag_service")
        StartupTime = 20
        Critical = $true
        WindowTitle = "Ingestion Service"
    }
}

# Desktop application configuration
$script:DesktopApp = @{
    Name = "Desktop Application"
    WorkingDir = Join-Path $ProjectRoot "desktop_app"
    Command = "npm run tauri:dev"
    Dependencies = @("ingestion_service", "rag_service")
    WindowTitle = "Email Assistant Desktop App"
}

# Startup order based on dependencies
$script:StartupOrder = @("qdrant", "ollama", "rag_service", "ingestion_service")

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    # Ensure logs directory exists
    $logsDir = Split-Path $script:LogFile
    if (-not (Test-Path $logsDir)) {
        New-Item -ItemType Directory -Path $logsDir -Force | Out-Null
    }
    
    # Write to log file
    Add-Content -Path $script:LogFile -Value $logEntry
    
    # Write to console with colors
    switch ($Level) {
        "ERROR" { Write-Host $logEntry -ForegroundColor Red }
        "WARN" { Write-Host $logEntry -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logEntry -ForegroundColor Green }
        "INFO" { Write-Host $logEntry -ForegroundColor Cyan }
        default { Write-Host $logEntry }
    }
}

function Test-Port {
    param([int]$Port, [int]$TimeoutSeconds = 5)
    
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $asyncResult = $tcpClient.BeginConnect("localhost", $Port, $null, $null)
        $wait = $asyncResult.AsyncWaitHandle.WaitOne($TimeoutSeconds * 1000, $false)
        
        if ($wait) {
            $tcpClient.EndConnect($asyncResult)
            $tcpClient.Close()
            return $true
        } else {
            $tcpClient.Close()
            return $false
        }
    } catch {
        return $false
    }
}

function Test-ServiceHealth {
    param([hashtable]$ServiceConfig, [int]$TimeoutSeconds = 10)
    
    if (-not $ServiceConfig.HealthUrl) {
        return Test-Port -Port $ServiceConfig.Port -TimeoutSeconds $TimeoutSeconds
    }
    
    try {
        $response = Invoke-WebRequest -Uri $ServiceConfig.HealthUrl -TimeoutSec $TimeoutSeconds -ErrorAction Stop
        return $response.StatusCode -eq 200
    } catch {
        if ($Verbose) {
            Write-Log "Health check failed for $($ServiceConfig.Name): $_" "WARN"
        }
        return $false
    }
}

function Start-ServiceInExternalConsole {
    param([string]$ServiceName, [hashtable]$ServiceConfig)
    
    Write-Log "Starting $($ServiceConfig.Name) in external console..."
    
    # Check if already running
    if (Test-ServiceHealth -ServiceConfig $ServiceConfig -TimeoutSeconds 3) {
        Write-Log "$($ServiceConfig.Name) is already running" "SUCCESS"
        return $true
    }
    
    # Validate working directory
    if (-not (Test-Path $ServiceConfig.WorkingDir)) {
        Write-Log "Working directory not found: $($ServiceConfig.WorkingDir)" "ERROR"
        return $false
    }
    
    try {
        # Create startup command for external console
        $startupCommand = "cd '$($ServiceConfig.WorkingDir)'; Write-Host 'Starting $($ServiceConfig.Name)...' -ForegroundColor Green; $($ServiceConfig.Command)"
        
        $startInfo = New-Object System.Diagnostics.ProcessStartInfo
        $startInfo.FileName = "powershell.exe"
        $startInfo.Arguments = "-NoExit -Command `"$startupCommand`""
        $startInfo.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Normal
        $startInfo.CreateNoWindow = $false
        
        # Set window title if supported
        if ($ServiceConfig.WindowTitle) {
            $titleCommand = "cd '$($ServiceConfig.WorkingDir)'; `$Host.UI.RawUI.WindowTitle = '$($ServiceConfig.WindowTitle)'; Write-Host 'Starting $($ServiceConfig.Name)...' -ForegroundColor Green; $($ServiceConfig.Command)"
            $startInfo.Arguments = "-NoExit -Command `"$titleCommand`""
        }
        
        $process = [System.Diagnostics.Process]::Start($startInfo)
        Write-Log "$($ServiceConfig.Name) started in external console with PID $($process.Id)" "SUCCESS"
        
        # Wait for service to become healthy
        Write-Log "Waiting for $($ServiceConfig.Name) to become healthy (max $($ServiceConfig.StartupTime) seconds)..."
        
        $elapsed = 0
        $interval = 2
        
        while ($elapsed -lt $ServiceConfig.StartupTime) {
            if (Test-ServiceHealth -ServiceConfig $ServiceConfig) {
                Write-Log "$($ServiceConfig.Name) is healthy and ready" "SUCCESS"
                return $true
            }
            
            Start-Sleep -Seconds $interval
            $elapsed += $interval
            
            if ($Verbose) {
                Write-Log "Waiting for $($ServiceConfig.Name)... ($elapsed/$($ServiceConfig.StartupTime) seconds)"
            }
        }
        
        Write-Log "$($ServiceConfig.Name) failed to become healthy within $($ServiceConfig.StartupTime) seconds" "ERROR"
        return $false
        
    } catch {
        Write-Log "Failed to start $($ServiceConfig.Name): $_" "ERROR"
        return $false
    }
}

function Test-Prerequisites {
    Write-Log "Checking system prerequisites..."
    
    $prerequisites = @(
        @{Name="PowerShell"; Command="powershell.exe"; Required=$true},
        @{Name="Python"; Command="python"; Required=$true},
        @{Name="Node.js"; Command="node"; Required=$true},
        @{Name="NPM"; Command="npm"; Required=$true},
        @{Name="Rust/Cargo"; Command="cargo"; Required=$true}
    )
    
    $allGood = $true
    
    foreach ($prereq in $prerequisites) {
        try {
            $null = Get-Command $prereq.Command -ErrorAction Stop
            Write-Log "✅ $($prereq.Name) is available" "SUCCESS"
        } catch {
            if ($prereq.Required) {
                Write-Log "❌ $($prereq.Name) is required but not found" "ERROR"
                $allGood = $false
            } else {
                Write-Log "⚠️ $($prereq.Name) is optional but not found" "WARN"
            }
        }
    }
    
    # Check for Ollama specifically
    $ollamaPath = "C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe"
    if (Test-Path $ollamaPath) {
        $env:PATH += ";$(Split-Path $ollamaPath)"
        Write-Log "✅ Ollama found and added to PATH" "SUCCESS"
    } else {
        Write-Log "❌ Ollama not found at expected location" "ERROR"
        $allGood = $false
    }
    
    return $allGood
}

function Start-AllServices {
    Write-Log "Starting all services in dependency order..." "INFO"
    
    $success = $true
    
    foreach ($serviceName in $script:StartupOrder) {
        $serviceConfig = $script:Services[$serviceName]
        
        Write-Host ""
        Write-Host "🔄 Starting $($serviceConfig.Name)..." -ForegroundColor Blue
        
        # Check dependencies
        foreach ($dependency in $serviceConfig.Dependencies) {
            $depConfig = $script:Services[$dependency]
            if (-not (Test-ServiceHealth -ServiceConfig $depConfig -TimeoutSeconds 5)) {
                Write-Log "Dependency $($depConfig.Name) is not healthy, cannot start $($serviceConfig.Name)" "ERROR"
                $success = $false
                break
            } else {
                Write-Log "✅ Dependency $($depConfig.Name) is healthy" "SUCCESS"
            }
        }
        
        if (-not $success -and $serviceConfig.Critical) {
            Write-Log "Critical service dependency failed, aborting startup" "ERROR"
            break
        }
        
        if (-not (Start-ServiceInExternalConsole -ServiceName $serviceName -ServiceConfig $serviceConfig)) {
            if ($serviceConfig.Critical) {
                Write-Log "Critical service $($serviceConfig.Name) failed to start, aborting" "ERROR"
                $success = $false
                break
            } else {
                Write-Log "Non-critical service $($serviceConfig.Name) failed to start, continuing" "WARN"
            }
        }
        
        # Brief pause between service starts
        Start-Sleep -Seconds 3
    }
    
    return $success
}

function Start-DesktopApplication {
    if ($SkipDesktop) {
        Write-Log "Skipping desktop application startup as requested" "INFO"
        return $true
    }

    Write-Host ""
    Write-Host "🖥️ Starting Desktop Application..." -ForegroundColor Blue

    # Check dependencies
    foreach ($dependency in $script:DesktopApp.Dependencies) {
        $depConfig = $script:Services[$dependency]
        if (-not (Test-ServiceHealth -ServiceConfig $depConfig -TimeoutSeconds 5)) {
            Write-Log "Dependency $($depConfig.Name) is not healthy, cannot start desktop app" "ERROR"
            return $false
        } else {
            Write-Log "✅ Dependency $($depConfig.Name) is healthy" "SUCCESS"
        }
    }

    # Validate working directory
    if (-not (Test-Path $script:DesktopApp.WorkingDir)) {
        Write-Log "Desktop app working directory not found: $($script:DesktopApp.WorkingDir)" "ERROR"
        return $false
    }

    try {
        Write-Log "Starting $($script:DesktopApp.Name) in external console..."

        $titleCommand = "cd '$($script:DesktopApp.WorkingDir)'; `$Host.UI.RawUI.WindowTitle = '$($script:DesktopApp.WindowTitle)'; Write-Host 'Starting $($script:DesktopApp.Name)...' -ForegroundColor Green; $($script:DesktopApp.Command)"

        $startInfo = New-Object System.Diagnostics.ProcessStartInfo
        $startInfo.FileName = "powershell.exe"
        $startInfo.Arguments = "-NoExit -Command `"$titleCommand`""
        $startInfo.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Normal
        $startInfo.CreateNoWindow = $false

        $process = [System.Diagnostics.Process]::Start($startInfo)
        Write-Log "$($script:DesktopApp.Name) started in external console with PID $($process.Id)" "SUCCESS"

        return $true

    } catch {
        Write-Log "Failed to start $($script:DesktopApp.Name): $_" "ERROR"
        return $false
    }
}

function Get-ServiceStatus {
    Write-Host ""
    Write-Host "📊 Service Status Check:" -ForegroundColor Cyan
    Write-Host "=" * 50 -ForegroundColor Cyan

    foreach ($serviceName in $script:StartupOrder) {
        $config = $script:Services[$serviceName]
        $isHealthy = Test-ServiceHealth -ServiceConfig $config -TimeoutSeconds 3
        $portOpen = Test-Port -Port $config.Port -TimeoutSeconds 2

        $status = if ($isHealthy) { "✅ HEALTHY" } elseif ($portOpen) { "⚠️ RUNNING" } else { "❌ STOPPED" }
        $color = if ($isHealthy) { "Green" } elseif ($portOpen) { "Yellow" } else { "Red" }

        Write-Host "$($config.Name) (Port $($config.Port)): $status" -ForegroundColor $color
    }
}

function Show-SystemInfo {
    Write-Host ""
    Write-Host "💻 System Information:" -ForegroundColor Cyan
    Write-Host "=" * 50 -ForegroundColor Cyan

    # Memory usage
    try {
        $memory = Get-WmiObject -Class Win32_OperatingSystem
        $memoryUsage = [math]::Round(($memory.TotalVisibleMemorySize - $memory.FreePhysicalMemory) / $memory.TotalVisibleMemorySize * 100, 2)
        $memoryColor = if ($memoryUsage -gt 80) { "Red" } elseif ($memoryUsage -gt 60) { "Yellow" } else { "Green" }
        Write-Host "Memory Usage: $memoryUsage%" -ForegroundColor $memoryColor
    } catch {
        Write-Host "Memory Usage: Unable to determine" -ForegroundColor Yellow
    }

    # Disk usage
    try {
        $disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
        $diskUsage = [math]::Round(($disk.Size - $disk.FreeSpace) / $disk.Size * 100, 2)
        $diskColor = if ($diskUsage -gt 90) { "Red" } elseif ($diskUsage -gt 75) { "Yellow" } else { "Green" }
        Write-Host "Disk Usage (C:): $diskUsage%" -ForegroundColor $diskColor
    } catch {
        Write-Host "Disk Usage: Unable to determine" -ForegroundColor Yellow
    }
}

# Main execution
Write-Log "=== AI Email Assistant Complete Startup Script Started ===" "INFO"

try {
    # Check prerequisites
    if (-not (Test-Prerequisites)) {
        Write-Log "Prerequisites check failed. Please install missing components." "ERROR"
        exit 1
    }

    Write-Host ""
    Write-Host "✅ Prerequisites check passed" -ForegroundColor Green

    # Start all services
    if (-not (Start-AllServices)) {
        Write-Log "Service startup failed" "ERROR"
        exit 1
    }

    # Start desktop application
    Start-DesktopApplication

    # Show final status
    Get-ServiceStatus
    Show-SystemInfo

    Write-Host ""
    Write-Host "🎉 All services started successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Service URLs:" -ForegroundColor Cyan
    Write-Host "- Qdrant Vector DB: http://localhost:6333" -ForegroundColor White
    Write-Host "- Ollama LLM: http://localhost:11434" -ForegroundColor White
    Write-Host "- RAG Service: http://localhost:8003" -ForegroundColor White
    Write-Host "- Ingestion Service: http://localhost:8080" -ForegroundColor White
    Write-Host ""
    Write-Host "📁 Log file: $script:LogFile" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🚀 Ready to process your 11GB mbox file!" -ForegroundColor Green
    Write-Host "Use the ingestion service API or desktop app to load:" -ForegroundColor White
    Write-Host "f:\EMAILS\full_combined_threaded.mbox" -ForegroundColor Yellow

} catch {
    Write-Log "Startup script failed: $_" "ERROR"
    exit 1
}

Write-Log "=== Startup script completed successfully ===" "SUCCESS"
