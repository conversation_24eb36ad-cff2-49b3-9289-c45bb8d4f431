[2025-07-11 20:10:51] [INFO] === AI Email Assistant Complete Startup Script Started ===
[2025-07-11 20:10:51] [INFO] Checking system prerequisites...
[2025-07-11 20:10:51] [SUCCESS] Python is available
[2025-07-11 20:10:51] [SUCCESS] Node.js is available
[2025-07-11 20:10:51] [SUCCESS] NPM is available
[2025-07-11 20:10:51] [SUCCESS] Rust/Cargo is available
[2025-07-11 20:10:51] [SUCCESS] Ollama found and added to PATH
[2025-07-11 20:10:51] [INFO] Starting Qdrant Vector Database in external console...
[2025-07-11 20:10:51] [SUCCESS] Qdrant Vector Database is already running on port 6333
[2025-07-11 20:10:54] [INFO] Starting RAG Service in external console...
[2025-07-11 20:10:54] [SUCCESS] RAG Service is already running on port 8003
[2025-07-11 20:10:57] [INFO] Starting Ingestion Service in external console...
[2025-07-11 20:10:57] [SUCCESS] Ingestion Service is already running on port 8080
[2025-07-11 20:11:00] [INFO] Starting Desktop Application in external console...
[2025-07-11 20:11:00] [SUCCESS] Desktop Application started in external console with PID 41556
[2025-07-11 20:11:09] [SUCCESS] === Startup script completed successfully ===
[2025-07-11 20:15:14] [INFO] === AI Email Assistant Complete Startup Script Started ===
[2025-07-11 20:15:14] [INFO] Checking system prerequisites...
[2025-07-11 20:15:14] [SUCCESS] Python is available
[2025-07-11 20:15:14] [SUCCESS] Node.js is available
[2025-07-11 20:15:14] [SUCCESS] NPM is available
[2025-07-11 20:15:14] [SUCCESS] Rust/Cargo is available
[2025-07-11 20:15:14] [SUCCESS] Ollama found and added to PATH
[2025-07-11 20:15:14] [INFO] Starting Qdrant Vector Database in external console...
[2025-07-11 20:15:14] [SUCCESS] Qdrant Vector Database is already running on port 6333
[2025-07-11 20:15:17] [INFO] Starting RAG Service in external console...
[2025-07-11 20:15:17] [SUCCESS] RAG Service is already running on port 8003
[2025-07-11 20:15:20] [INFO] Starting Ingestion Service in external console...
[2025-07-11 20:15:20] [SUCCESS] Ingestion Service is already running on port 8080
[2025-07-11 20:15:32] [SUCCESS] === Startup script completed successfully ===
