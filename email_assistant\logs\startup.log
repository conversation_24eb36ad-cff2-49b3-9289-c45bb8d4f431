[2025-07-12 00:20:20] [INFO] === AI Email Assistant Complete Startup Script Started ===
[2025-07-12 00:20:20] [INFO] Checking system prerequisites...
[2025-07-12 00:20:20] [SUCCESS] Python is available
[2025-07-12 00:20:20] [SUCCESS] Node.js is available
[2025-07-12 00:20:20] [SUCCESS] NPM is available
[2025-07-12 00:20:20] [SUCCESS] Rust/Cargo is available
[2025-07-12 00:20:20] [SUCCESS] Ollama found and added to PATH
[2025-07-12 00:20:20] [INFO] Starting Qdrant Vector Database in external console...
[2025-07-12 00:20:23] [SUCCESS] Qdrant Vector Database started in external console with PID 14844
[2025-07-12 00:20:23] [INFO] Waiting for Qdrant Vector Database to become healthy (max 15 seconds)...
[2025-07-12 00:20:25] [SUCCESS] Qdrant Vector Database is healthy and ready
[2025-07-12 00:20:28] [INFO] Starting RAG Service in external console...
[2025-07-12 00:20:30] [SUCCESS] RAG Service started in external console with PID 12100
[2025-07-12 00:20:30] [INFO] Waiting for RAG Service to become healthy (max 25 seconds)...
[2025-07-12 00:21:04] [SUCCESS] RAG Service is healthy and ready
[2025-07-12 00:21:07] [INFO] Starting Ingestion Service in external console...
[2025-07-12 00:21:09] [SUCCESS] Ingestion Service started in external console with PID 3820
[2025-07-12 00:21:09] [INFO] Waiting for Ingestion Service to become healthy (max 20 seconds)...
