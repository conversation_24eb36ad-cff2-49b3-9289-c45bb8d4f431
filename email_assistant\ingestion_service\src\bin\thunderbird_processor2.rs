use ingestion_service::{parse_mbox_streaming, ParsedEmail};
use std::collections::HashMap;
use std::env;
use std::fs::{File, OpenOptions};
use std::io::{<PERSON>ufWriter, Write, BufRead, BufReader};
use std::path::Path;
use uuid::Uuid;
use serde::{Serialize, Deserialize};
use rand::random;

#[derive(Debug, Serialize, Deserialize, Clone)]
struct ThreadInfo {
    thread_id: Uuid,
    case_id: Option<Uuid>,
    message_ids: Vec<String>,
    subject_normalized: String,
    participants: Vec<String>,
    email_count: u32,
    first_date: String,
    last_date: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct CaseInfo {
    case_id: Uuid,
    subject_pattern: String,
    thread_ids: Vec<Uuid>,
    email_count: u32,
    participants: Vec<String>,
    first_date: String,
    last_date: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct ThreadingState {
    threads: HashMap<Uuid, ThreadInfo>,
    cases: HashMap<Uuid, CaseInfo>,
    message_id_to_thread: HashMap<String, Uuid>,
    subject_to_thread: HashMap<String, Uuid>,
    subject_to_case: HashMap<String, Uuid>,
    next_thread_id: u64,
    next_case_id: u64,
}

impl ThreadingState {
    fn new() -> Self {
        Self {
            threads: HashMap::new(),
            cases: HashMap::new(),
            message_id_to_thread: HashMap::new(),
            subject_to_thread: HashMap::new(),
            subject_to_case: HashMap::new(),
            next_thread_id: 1,
            next_case_id: 1,
        }
    }

    fn save_to_file(&self, path: &str) -> Result<(), Box<dyn std::error::Error>> {
        let file = File::create(path)?;
        let writer = BufWriter::new(file);
        serde_json::to_writer_pretty(writer, self)?;
        Ok(())
    }

    fn load_from_file(path: &str) -> Result<Self, Box<dyn std::error::Error>> {
        if !Path::new(path).exists() {
            return Ok(Self::new());
        }
        let file = File::open(path)?;
        let reader = BufReader::new(file);
        let state = serde_json::from_reader(reader)?;
        Ok(state)
    }
}

struct StreamingProcessor {
    threading_state: ThreadingState,
    output_writer: BufWriter<File>,
    threading_file_path: String,
    email_buffer: Vec<String>,
    buffer_size: usize,
    processed_count: u32,
    total_threads: u32,
    total_cases: u32,
}

impl StreamingProcessor {
    fn new(output_path: &str, threading_path: &str, buffer_size: usize) -> Result<Self, Box<dyn std::error::Error>> {
        let output_file = File::create(output_path)?;
        let output_writer = BufWriter::new(output_file);
        
        let threading_state = ThreadingState::load_from_file(threading_path).unwrap_or_else(|_| ThreadingState::new());
        
        Ok(Self {
            threading_state,
            output_writer,
            threading_file_path: threading_path.to_string(),
            email_buffer: Vec::new(),
            buffer_size,
            processed_count: 0,
            total_threads: 0,
            total_cases: 0,
        })
    }

    fn normalize_subject(&self, subject: &str) -> String {
        let mut normalized = subject.to_lowercase();
        // Remove common prefixes
        for prefix in &["re:", "fwd:", "fw:", "aw:", "sv:", "vs:", "wg:"] {
            if normalized.starts_with(prefix) {
                normalized = normalized[prefix.len()..].trim().to_string();
            }
        }
        // Remove extra whitespace
        normalized = normalized.split_whitespace().collect::<Vec<_>>().join(" ");
        normalized
    }

    fn extract_participants(&self, email: &ParsedEmail) -> Vec<String> {
        let mut participants = Vec::new();

        // Add sender
        if let Some(ref from) = email.from {
            if !from.is_empty() {
                participants.push(from.clone());
            }
        }

        // Add recipients
        for recipient in &email.to {
            if !recipient.is_empty() && !participants.contains(recipient) {
                participants.push(recipient.clone());
            }
        }

        participants
    }

    fn process_email(&mut self, email: ParsedEmail) -> Result<String, Box<dyn std::error::Error>> {
        let default_subject = "No Subject".to_string();
        let subject = email.subject.as_ref().unwrap_or(&default_subject);
        let normalized_subject = self.normalize_subject(subject);
        let participants = self.extract_participants(&email);

        // Generate message ID if missing
        let message_id = format!("<generated-{}-{}@local>",
                               chrono::Utc::now().timestamp_millis(),
                               rand::random::<u32>());

        // Find or create thread
        let thread_id = if let Some(&existing_thread_id) = self.threading_state.message_id_to_thread.get(&message_id) {
            existing_thread_id
        } else if let Some(&existing_thread_id) = self.threading_state.subject_to_thread.get(&normalized_subject) {
            existing_thread_id
        } else {
            // Create new thread
            let thread_id = Uuid::new_v4();
            let sent_date_str = email.sent_date.map(|d| d.to_rfc3339()).unwrap_or_else(|| "Unknown".to_string());
            let thread_info = ThreadInfo {
                thread_id,
                case_id: None,
                message_ids: vec![message_id.clone()],
                subject_normalized: normalized_subject.clone(),
                participants: participants.clone(),
                email_count: 1,
                first_date: sent_date_str.clone(),
                last_date: sent_date_str,
            };

            self.threading_state.threads.insert(thread_id, thread_info);
            self.threading_state.message_id_to_thread.insert(message_id.clone(), thread_id);
            self.threading_state.subject_to_thread.insert(normalized_subject.clone(), thread_id);
            self.total_threads += 1;

            thread_id
        };

        // Update thread info
        if let Some(thread_info) = self.threading_state.threads.get_mut(&thread_id) {
            if !thread_info.message_ids.contains(&message_id) {
                thread_info.message_ids.push(message_id.clone());
                thread_info.email_count += 1;
                let sent_date_str = email.sent_date.map(|d| d.to_rfc3339()).unwrap_or_else(|| "Unknown".to_string());
                thread_info.last_date = sent_date_str;

                // Update participants
                for participant in &participants {
                    if !thread_info.participants.contains(participant) {
                        thread_info.participants.push(participant.clone());
                    }
                }
            }
        }

        // Find or create case
        let case_id = if let Some(&existing_case_id) = self.threading_state.subject_to_case.get(&normalized_subject) {
            existing_case_id
        } else {
            // Create new case
            let case_id = Uuid::new_v4();
            let sent_date_str = email.sent_date.map(|d| d.to_rfc3339()).unwrap_or_else(|| "Unknown".to_string());
            let case_info = CaseInfo {
                case_id,
                subject_pattern: normalized_subject.clone(),
                thread_ids: vec![thread_id],
                email_count: 1,
                participants: participants.clone(),
                first_date: sent_date_str.clone(),
                last_date: sent_date_str,
            };

            self.threading_state.cases.insert(case_id, case_info);
            self.threading_state.subject_to_case.insert(normalized_subject.clone(), case_id);
            self.total_cases += 1;

            case_id
        };

        // Update case info
        if let Some(case_info) = self.threading_state.cases.get_mut(&case_id) {
            if !case_info.thread_ids.contains(&thread_id) {
                case_info.thread_ids.push(thread_id);
            }
            case_info.email_count += 1;
            let sent_date_str = email.sent_date.map(|d| d.to_rfc3339()).unwrap_or_else(|| "Unknown".to_string());
            case_info.last_date = sent_date_str;

            // Update participants
            for participant in &participants {
                if !case_info.participants.contains(participant) {
                    case_info.participants.push(participant.clone());
                }
            }
        }

        // Update thread with case_id
        if let Some(thread_info) = self.threading_state.threads.get_mut(&thread_id) {
            thread_info.case_id = Some(case_id);
        }

        // Create enhanced email output
        let sent_date_str = email.sent_date.map(|d| d.to_rfc3339()).unwrap_or_else(|| "Unknown".to_string());
        let enhanced_email = format!(
            "From - {}\n\
            X-Thread-ID: {}\n\
            X-Case-ID: {}\n\
            X-Thread-Count: {}\n\
            X-Case-Count: {}\n\
            X-Participants: {}\n\
            Message-ID: {}\n\
            Subject: {}\n\
            From: {}\n\
            To: {}\n\
            Date: {}\n\
            \n\
            {}\n\
            \n",
            sent_date_str,
            thread_id,
            case_id,
            self.threading_state.threads.get(&thread_id).map(|t| t.email_count).unwrap_or(0),
            self.threading_state.cases.get(&case_id).map(|c| c.email_count).unwrap_or(0),
            participants.join(", "),
            message_id,
            subject,
            email.from.as_ref().unwrap_or(&"Unknown".to_string()),
            email.to.join(", "),
            sent_date_str,
            email.cleaned_plain_text_body.unwrap_or_else(|| "No content".to_string())
        );

        Ok(enhanced_email)
    }

    fn flush_buffer(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        for email_content in &self.email_buffer {
            self.output_writer.write_all(email_content.as_bytes())?;
        }
        self.output_writer.flush()?;
        self.email_buffer.clear();
        
        // Save threading state
        self.threading_state.save_to_file(&self.threading_file_path)?;
        
        Ok(())
    }

    fn add_email(&mut self, email: ParsedEmail) -> Result<(), Box<dyn std::error::Error>> {
        let enhanced_email = self.process_email(email)?;
        self.email_buffer.push(enhanced_email);
        self.processed_count += 1;

        if self.email_buffer.len() >= self.buffer_size {
            self.flush_buffer()?;
            println!("  Processed {} emails (Threads: {}, Cases: {})", 
                     self.processed_count, self.total_threads, self.total_cases);
        }

        Ok(())
    }

    fn finalize(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if !self.email_buffer.is_empty() {
            self.flush_buffer()?;
        }
        
        // Final save of threading state
        self.threading_state.save_to_file(&self.threading_file_path)?;
        
        println!("\n=== PROCESSING COMPLETE ===");
        println!("Total emails processed: {}", self.processed_count);
        println!("Total threads created: {}", self.total_threads);
        println!("Total cases created: {}", self.total_cases);
        println!("Threading data saved to: {}", self.threading_file_path);
        
        Ok(())
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = env::args().collect();
    if args.len() < 3 {
        eprintln!("Usage: {} <input_mbox> <output_mbox> [buffer_size]", args[0]);
        eprintln!("  buffer_size: Number of emails to buffer before writing (default: 100)");
        std::process::exit(1);
    }

    let input_path = &args[1];
    let output_path = &args[2];
    let buffer_size = args.get(3)
        .and_then(|s| s.parse().ok())
        .unwrap_or(100);

    let threading_path = format!("{}.threading.json", output_path);

    println!("=== THUNDERBIRD PROCESSOR 2 (STREAMING) ===");
    println!("Input file: {}", input_path);
    println!("Output file: {}", output_path);
    println!("Threading data: {}", threading_path);
    println!("Buffer size: {} emails", buffer_size);
    println!();

    let input_size = std::fs::metadata(input_path)?.len();
    let input_size_mb = input_size as f64 / (1024.0 * 1024.0);
    println!("Input file size: {:.1} MB", input_size_mb);

    if input_size_mb > 1000.0 {
        println!("⚠️  Large file detected - using streaming processing");
    }

    let mut processor = StreamingProcessor::new(output_path, &threading_path, buffer_size)?;

    println!("Starting streaming processing...");
    let start_time = std::time::Instant::now();

    // Stream and process emails
    parse_mbox_streaming(input_path, |email| {
        if let Err(e) = processor.add_email(email) {
            eprintln!("Error processing email: {}", e);
        }
    })?;

    processor.finalize()?;

    let duration = start_time.elapsed();
    println!("Processing completed in {:.2} seconds", duration.as_secs_f64());

    Ok(())
}
