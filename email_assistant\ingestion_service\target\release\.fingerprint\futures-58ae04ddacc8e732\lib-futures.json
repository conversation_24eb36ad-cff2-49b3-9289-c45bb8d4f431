{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 18348216721672176038, "path": 13236182631312062850, "deps": [[5103565458935487, "futures_io", false, 1538967178778091614], [1811549171721445101, "futures_channel", false, 15165701454039345717], [7013762810557009322, "futures_sink", false, 15543802659000362540], [7620660491849607393, "futures_core", false, 4359839236934194262], [10629569228670356391, "futures_util", false, 6321976705103560301], [12779779637805422465, "futures_executor", false, 1668050908744894966], [16240732885093539806, "futures_task", false, 13175841475487866116]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-58ae04ddacc8e732\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}