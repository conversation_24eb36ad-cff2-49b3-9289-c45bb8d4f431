# AI-Assisted Email Response System - Service Startup Guide

This directory contains scripts to automatically start all required services for the AI-Assisted Email Response System.

## Quick Start

### Option 1: Double-click the batch file
```
start_all_services.bat
```

### Option 2: Run PowerShell script directly
```powershell
.\start_all_services.ps1
```

## Script Options

### PowerShell Script Parameters

- `-Verbose`: Enable detailed logging and progress information
- `-SkipDesktop`: Skip launching the desktop application
- `-Timeout`: Set timeout for service operations (default: 60 seconds)

### Examples

```powershell
# Start all services with verbose output
.\start_all_services.ps1 -Verbose

# Start only backend services (skip desktop app)
.\start_all_services.ps1 -SkipDesktop

# Start with custom timeout
.\start_all_services.ps1 -Timeout 120
```

## Services Started

The script automatically starts these services in external console windows:

1. **Qdrant Vector Database** (Port 6333)
   - Vector storage for email embeddings
   - Health check: http://localhost:6333/

2. **RAG Service** (Port 8003)
   - Python service for embeddings and RAG processing
   - Health check: http://localhost:8003/health

3. **Ingestion Service** (Port 8080)
   - Rust service for email parsing and ingestion
   - Health check: http://localhost:8080/health

4. **Desktop Application**
   - Tauri-based desktop interface
   - Launched via `npm run tauri:dev`

## Prerequisites

The script automatically checks for these required components:

- Python (for RAG service)
- Node.js and NPM (for desktop app)
- Rust and Cargo (for ingestion service)
- Ollama (for LLM functionality)

## File Processing

Once all services are running, you can process your 11GB mbox file:

**File Location**: `f:\EMAILS\full_combined_threaded.mbox`

**Processing Options**:
1. Use the desktop application interface (recommended)
2. Use the ingestion service API directly
3. Use command line tools in the ingestion_service directory

## Logs

Service startup logs are saved to:
```
logs/startup.log
```

## Troubleshooting

### Common Issues

1. **Port Already in Use**: The script detects if services are already running
2. **Missing Prerequisites**: Install Python, Node.js, Rust, and Ollama
3. **Permission Issues**: Run PowerShell as Administrator if needed

### Manual Service Management

If you need to start services individually:

```powershell
# Qdrant
cd ingestion_service\qdrant
.\qdrant.exe

# RAG Service
cd rag_service
python main.py

# Ingestion Service
cd ingestion_service
cargo run --bin ingestion_server

# Desktop App
cd desktop_app
npm run tauri:dev
```

## Service URLs

Once started, services are available at:

- **Qdrant**: http://localhost:6333
- **Ollama**: http://localhost:11434
- **RAG Service**: http://localhost:8003
- **Ingestion Service**: http://localhost:8080

## Large File Processing

The system is optimized for large files like your 11GB mbox:

- **Streaming Processing**: Files > 1GB use memory-efficient streaming
- **Progress Monitoring**: Real-time progress updates every 300 emails
- **Error Handling**: Robust handling of problematic emails
- **Memory Management**: Keeps memory usage under 200MB regardless of file size

Ready to process your email data!
